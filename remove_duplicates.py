import pandas as pd
import shutil

# 备份原文件
try:
    shutil.copy2('选择题.xlsx', '选择题_backup.xlsx')
    print("已创建备份文件: 选择题_backup.xlsx")
except Exception as e:
    print(f"备份失败: {str(e)}")
    exit(1)

# 读取Excel文件
try:
    df = pd.read_excel('选择题.xlsx')
    # 假设A列列名为'题目'，如果实际列名不同需要修改此处
    if '题目' not in df.columns:
        # 如果没有列名，使用第一列
        df.columns = ['题目'] + list(df.columns[1:])
        print("已自动识别题目列为第一列")
except Exception as e:
    print(f"读取文件失败: {str(e)}")
    exit(1)

# 删除重复题目行
duplicate_count = df.duplicated(subset=['题目']).sum()
if duplicate_count > 0:
    df.drop_duplicates(subset=['题目'], keep='first', inplace=True)
    # 保存修改后的文件
    df.to_excel('选择题.xlsx', index=False)
    print(f"成功删除 {duplicate_count} 条重复题目")
else:
    print("未发现重复题目")