{% extends "base.html" %}

{% block title %}添加学生 - 学生信息管理系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-plus"></i> 添加学生信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="studentForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    姓名 <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       required 
                                       maxlength="100"
                                       placeholder="请输入学生姓名">
                                <div class="form-text">必填项，最多100个字符</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="age" class="form-label">
                                    年龄 <span class="text-danger">*</span>
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="age" 
                                       name="age" 
                                       required 
                                       min="1" 
                                       max="150"
                                       placeholder="请输入年龄">
                                <div class="form-text">必填项，1-150之间的整数</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="height" class="form-label">身高 (cm)</label>
                                <input type="number" 
                                       class="form-control" 
                                       id="height" 
                                       name="height" 
                                       step="0.1" 
                                       min="50" 
                                       max="300"
                                       placeholder="请输入身高">
                                <div class="form-text">可选项，支持小数</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="major" class="form-label">专业</label>
                                <input type="text" 
                                       class="form-control" 
                                       id="major" 
                                       name="major" 
                                       maxlength="100"
                                       placeholder="请输入专业名称">
                                <div class="form-text">可选项，最多100个字符</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="remarks" class="form-label">备注</label>
                        <textarea class="form-control" 
                                  id="remarks" 
                                  name="remarks" 
                                  rows="3" 
                                  maxlength="500"
                                  placeholder="请输入备注信息（可选）"></textarea>
                        <div class="form-text">可选项，最多500个字符</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <div>
                            <button type="reset" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('studentForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const age = document.getElementById('age').value;
    
    if (!name) {
        alert('请输入学生姓名！');
        e.preventDefault();
        return false;
    }
    
    if (!age || age < 1 || age > 150) {
        alert('请输入有效的年龄（1-150之间）！');
        e.preventDefault();
        return false;
    }
    
    return true;
});

// 实时字符计数
document.getElementById('remarks').addEventListener('input', function() {
    const maxLength = 500;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    let formText = this.nextElementSibling;
    if (remaining < 50) {
        formText.innerHTML = `可选项，还可输入 ${remaining} 个字符`;
        formText.className = remaining < 10 ? 'form-text text-danger' : 'form-text text-warning';
    } else {
        formText.innerHTML = '可选项，最多500个字符';
        formText.className = 'form-text';
    }
});
</script>
{% endblock %}
