# 学生信息管理系统

一个基于Flask和MySQL的学生信息管理系统，支持学生信息的增删改查和数据导出功能。

## 功能特性

- ✅ **学生信息管理**：添加、编辑、删除、查看学生信息
- ✅ **数据字段**：姓名、年龄、身高、专业、备注
- ✅ **数据导出**：支持导出为Excel和CSV格式
- ✅ **响应式设计**：基于Bootstrap 5，支持移动端访问
- ✅ **数据验证**：前端和后端双重数据验证
- ✅ **统计信息**：显示学生总数、平均年龄、平均身高等统计数据

## 技术栈

- **后端**：Python Flask
- **数据库**：MySQL
- **前端**：HTML5, CSS3, JavaScript, Bootstrap 5
- **数据处理**：Pandas, OpenPyXL
- **数据库ORM**：SQLAlchemy

## 安装和运行

### 1. 环境要求

- Python 3.7+
- MySQL 5.7+ 或 MySQL 8.0+

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置数据库

1. 确保MySQL服务正在运行
2. 修改 `config.py` 或 `init_db.py` 中的数据库连接参数：
   - 主机地址（默认：localhost）
   - 端口号（默认：3306）
   - 用户名（默认：root）
   - 密码（请修改为您的MySQL密码）
   - 数据库名（默认：student_db）

### 4. 初始化数据库

```bash
python init_db.py
```

这个脚本会：
- 创建数据库（如果不存在）
- 创建数据表
- 添加一些示例数据

### 5. 运行应用

```bash
python app.py
```

应用将在 `http://localhost:5000` 启动。

## 使用说明

### 主要功能

1. **查看学生列表**
   - 访问首页查看所有学生信息
   - 显示统计信息（总数、平均年龄、平均身高等）

2. **添加学生**
   - 点击"添加学生"按钮
   - 填写学生信息（姓名和年龄为必填项）
   - 提交保存

3. **编辑学生**
   - 在学生列表中点击"编辑"按钮
   - 修改学生信息
   - 保存更新

4. **删除学生**
   - 在学生列表中点击"删除"按钮
   - 确认删除操作

5. **导出数据**
   - 点击导航栏中的"导出数据"
   - 选择Excel或CSV格式
   - 下载文件

### 数据字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| 姓名 | 字符串 | 是 | 最多100个字符 |
| 年龄 | 整数 | 是 | 1-150之间 |
| 身高 | 浮点数 | 否 | 单位：厘米，支持小数 |
| 专业 | 字符串 | 否 | 最多100个字符 |
| 备注 | 文本 | 否 | 最多500个字符 |

## 项目结构

```
student-management-system/
├── app.py                 # 主应用文件
├── config.py             # 配置文件
├── init_db.py            # 数据库初始化脚本
├── requirements.txt      # 依赖包列表
├── README.md            # 项目说明
└── templates/           # HTML模板目录
    ├── base.html        # 基础模板
    ├── index.html       # 主页模板
    ├── add.html         # 添加学生模板
    └── edit.html        # 编辑学生模板
```

## 开发说明

### 数据库模型

```python
class Student(db.Model):
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False)
    age = db.Column(db.Integer, nullable=False)
    height = db.Column(db.Float, nullable=True)
    major = db.Column(db.String(100), nullable=True)
    remarks = db.Column(db.Text, nullable=True)
```

### API端点

- `GET /` - 显示学生列表
- `GET /add` - 显示添加学生表单
- `POST /add` - 提交新学生信息
- `GET /edit/<id>` - 显示编辑学生表单
- `POST /edit/<id>` - 更新学生信息
- `GET /delete/<id>` - 删除学生
- `GET /export/excel` - 导出Excel文件
- `GET /export/csv` - 导出CSV文件

## 常见问题

### 1. 数据库连接失败

- 检查MySQL服务是否启动
- 确认数据库连接参数是否正确
- 检查用户权限

### 2. 导入依赖包失败

```bash
# 升级pip
pip install --upgrade pip

# 如果网络问题，使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 3. 端口被占用

修改 `app.py` 中的端口号：

```python
app.run(debug=True, host='0.0.0.0', port=5001)  # 改为其他端口
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
