from flask import Flask, render_template, request, redirect, url_for, flash, make_response
from flask_sqlalchemy import SQLAlchemy
import pandas as pd
from io import BytesIO
import pymysql

# 安装PyMySQL作为MySQLdb的替代
pymysql.install_as_MySQLdb()

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'

# 数据库配置 - 请根据您的MySQL配置修改
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql://root:password@localhost/student_db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# 学生信息模型
class Student(db.Model):
    __tablename__ = 'students'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    name = db.Column(db.String(100), nullable=False)
    age = db.Column(db.Integer, nullable=False)
    height = db.Column(db.Float, nullable=True)
    major = db.Column(db.String(100), nullable=True)
    remarks = db.Column(db.Text, nullable=True)
    
    def __repr__(self):
        return f'<Student {self.name}>'

# 创建数据库表
with app.app_context():
    db.create_all()

# 主页 - 显示所有学生
@app.route('/')
def index():
    students = Student.query.all()
    return render_template('index.html', students=students)

# 添加学生
@app.route('/add', methods=['GET', 'POST'])
def add_student():
    if request.method == 'POST':
        name = request.form['name']
        age = request.form['age']
        height = request.form.get('height')
        major = request.form.get('major')
        remarks = request.form.get('remarks')
        
        # 数据验证
        if not name or not age:
            flash('姓名和年龄是必填项！', 'error')
            return render_template('add.html')
        
        try:
            age = int(age)
            height = float(height) if height else None
        except ValueError:
            flash('年龄必须是整数，身高必须是数字！', 'error')
            return render_template('add.html')
        
        student = Student(
            name=name,
            age=age,
            height=height,
            major=major,
            remarks=remarks
        )
        
        try:
            db.session.add(student)
            db.session.commit()
            flash('学生信息添加成功！', 'success')
            return redirect(url_for('index'))
        except Exception as e:
            db.session.rollback()
            flash(f'添加失败：{str(e)}', 'error')
    
    return render_template('add.html')

# 编辑学生
@app.route('/edit/<int:id>', methods=['GET', 'POST'])
def edit_student(id):
    student = Student.query.get_or_404(id)
    
    if request.method == 'POST':
        student.name = request.form['name']
        student.age = request.form['age']
        student.height = request.form.get('height')
        student.major = request.form.get('major')
        student.remarks = request.form.get('remarks')
        
        # 数据验证
        if not student.name or not student.age:
            flash('姓名和年龄是必填项！', 'error')
            return render_template('edit.html', student=student)
        
        try:
            student.age = int(student.age)
            student.height = float(student.height) if student.height else None
        except ValueError:
            flash('年龄必须是整数，身高必须是数字！', 'error')
            return render_template('edit.html', student=student)
        
        try:
            db.session.commit()
            flash('学生信息更新成功！', 'success')
            return redirect(url_for('index'))
        except Exception as e:
            db.session.rollback()
            flash(f'更新失败：{str(e)}', 'error')
    
    return render_template('edit.html', student=student)

# 删除学生
@app.route('/delete/<int:id>')
def delete_student(id):
    student = Student.query.get_or_404(id)
    
    try:
        db.session.delete(student)
        db.session.commit()
        flash('学生信息删除成功！', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'删除失败：{str(e)}', 'error')
    
    return redirect(url_for('index'))

# 导出为Excel
@app.route('/export/excel')
def export_excel():
    students = Student.query.all()
    
    # 创建数据列表
    data = []
    for student in students:
        data.append({
            'ID': student.id,
            '姓名': student.name,
            '年龄': student.age,
            '身高': student.height,
            '专业': student.major,
            '备注': student.remarks
        })
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 创建Excel文件
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='学生信息', index=False)
    
    output.seek(0)
    
    # 创建响应
    response = make_response(output.read())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    response.headers['Content-Disposition'] = 'attachment; filename=students.xlsx'
    
    return response

# 导出为CSV
@app.route('/export/csv')
def export_csv():
    students = Student.query.all()
    
    # 创建数据列表
    data = []
    for student in students:
        data.append({
            'ID': student.id,
            '姓名': student.name,
            '年龄': student.age,
            '身高': student.height,
            '专业': student.major,
            '备注': student.remarks
        })
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 创建CSV
    output = BytesIO()
    df.to_csv(output, index=False, encoding='utf-8-sig')  # utf-8-sig for Excel compatibility
    output.seek(0)
    
    # 创建响应
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv; charset=utf-8'
    response.headers['Content-Disposition'] = 'attachment; filename=students.csv'
    
    return response

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
