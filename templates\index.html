{% extends "base.html" %}

{% block title %}学生信息列表 - 学生信息管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users"></i> 学生信息列表
                </h5>
                <div>
                    <span class="badge bg-info">总计: {{ students|length }} 人</span>
                    <a href="{{ url_for('add_student') }}" class="btn btn-primary btn-sm ms-2">
                        <i class="fas fa-plus"></i> 添加学生
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if students %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>姓名</th>
                                    <th>年龄</th>
                                    <th>身高 (cm)</th>
                                    <th>专业</th>
                                    <th>备注</th>
                                    <th width="150">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for student in students %}
                                <tr>
                                    <td>{{ student.id }}</td>
                                    <td>
                                        <strong>{{ student.name }}</strong>
                                    </td>
                                    <td>{{ student.age }} 岁</td>
                                    <td>
                                        {% if student.height %}
                                            {{ "%.1f"|format(student.height) }}
                                        {% else %}
                                            <span class="text-muted">未填写</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if student.major %}
                                            {{ student.major }}
                                        {% else %}
                                            <span class="text-muted">未填写</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if student.remarks %}
                                            {% if student.remarks|length > 30 %}
                                                <span title="{{ student.remarks }}">
                                                    {{ student.remarks[:30] }}...
                                                </span>
                                            {% else %}
                                                {{ student.remarks }}
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">无</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_student', id=student.id) }}" 
                                               class="btn btn-outline-primary btn-sm" 
                                               title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('delete_student', id=student.id) }}" 
                                               class="btn btn-outline-danger btn-sm" 
                                               title="删除"
                                               onclick="return confirmDelete('{{ student.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无学生信息</h5>
                        <p class="text-muted">点击上方"添加学生"按钮开始添加学生信息</p>
                        <a href="{{ url_for('add_student') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 添加第一个学生
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if students %}
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-chart-bar"></i> 统计信息
                </h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ students|length }}</h4>
                            <small class="text-muted">总学生数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">
                                {% set avg_age = students|map(attribute='age')|list|sum / students|length %}
                                {{ "%.1f"|format(avg_age) }}
                            </h4>
                            <small class="text-muted">平均年龄</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">
                                {% set heights = students|selectattr('height')|map(attribute='height')|list %}
                                {% if heights %}
                                    {{ "%.1f"|format(heights|sum / heights|length) }}
                                {% else %}
                                    --
                                {% endif %}
                            </h4>
                            <small class="text-muted">平均身高 (cm)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">
                                {{ students|selectattr('major')|list|length }}
                            </h4>
                            <small class="text-muted">已填写专业</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
