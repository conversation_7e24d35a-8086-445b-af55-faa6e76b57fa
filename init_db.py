#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库和表结构
"""

import pymysql
from app import app, db, Student

def create_database():
    """创建数据库（如果不存在）"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='password',  # 请修改为您的MySQL密码
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute("CREATE DATABASE IF NOT EXISTS student_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("数据库 'student_db' 创建成功或已存在")
        
        connection.commit()
        connection.close()
        
    except Exception as e:
        print(f"创建数据库时出错: {e}")
        return False
    
    return True

def create_tables():
    """创建数据表"""
    try:
        with app.app_context():
            # 创建所有表
            db.create_all()
            print("数据表创建成功")
            
            # 检查是否有示例数据
            if Student.query.count() == 0:
                print("正在添加示例数据...")
                add_sample_data()
            else:
                print("数据表中已有数据，跳过示例数据添加")
                
    except Exception as e:
        print(f"创建数据表时出错: {e}")
        return False
    
    return True

def add_sample_data():
    """添加示例数据"""
    try:
        sample_students = [
            {
                'name': '张三',
                'age': 20,
                'height': 175.5,
                'major': '计算机科学与技术',
                'remarks': '学习成绩优秀，积极参与课外活动'
            },
            {
                'name': '李四',
                'age': 19,
                'height': 168.0,
                'major': '软件工程',
                'remarks': '编程能力强，团队合作意识好'
            },
            {
                'name': '王五',
                'age': 21,
                'height': 180.2,
                'major': '数据科学与大数据技术',
                'remarks': '对数据分析有浓厚兴趣'
            },
            {
                'name': '赵六',
                'age': 20,
                'height': None,
                'major': '人工智能',
                'remarks': None
            },
            {
                'name': '钱七',
                'age': 22,
                'height': 165.8,
                'major': None,
                'remarks': '转专业学生，学习态度认真'
            }
        ]
        
        with app.app_context():
            for student_data in sample_students:
                student = Student(**student_data)
                db.session.add(student)
            
            db.session.commit()
            print(f"成功添加 {len(sample_students)} 条示例数据")
            
    except Exception as e:
        print(f"添加示例数据时出错: {e}")
        db.session.rollback()

def main():
    """主函数"""
    print("开始初始化数据库...")
    
    # 1. 创建数据库
    if not create_database():
        print("数据库创建失败，程序退出")
        return
    
    # 2. 创建数据表
    if not create_tables():
        print("数据表创建失败，程序退出")
        return
    
    print("数据库初始化完成！")
    print("\n您现在可以运行以下命令启动应用:")
    print("python app.py")
    print("\n然后在浏览器中访问: http://localhost:5000")

if __name__ == '__main__':
    main()
